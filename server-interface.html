<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Logger Test Server</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .success {
            color: #28a745;
            background-color: #d4edda;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
        }
        .error {
            color: #dc3545;
            background-color: #f8d7da;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
        }
        .token-info {
            background-color: #e9ecef;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
        }
        #output {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            border: 1px solid #dee2e6;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Logger Test Server</h1>
        <p>Server running on <strong>localhost:3000</strong></p>
        
        <div class="token-info">
            <h3>📋 Token Status</h3>
            <div id="token-status">Loading...</div>
            <button onclick="refreshTokenInfo()">🔄 Refresh Token Info</button>
        </div>
    </div>

    <div class="container">
        <h2>📝 Send Log Entry</h2>
        <form id="logForm">
            <div class="form-group">
                <label for="stack">Stack:</label>
                <select id="stack" required>
                    <option value="">Select stack...</option>
                    <option value="backend">Backend</option>
                    <option value="frontend">Frontend</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="level">Level:</label>
                <select id="level" required>
                    <option value="">Select level...</option>
                    <option value="error">Error</option>
                    <option value="warn">Warning</option>
                    <option value="info">Info</option>
                    <option value="debug">Debug</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="package">Package:</label>
                <select id="package" required>
                    <option value="">Select package...</option>
                    <option value="auth">Auth</option>
                    <option value="handler">Handler</option>
                    <option value="middleware">Middleware</option>
                    <option value="db">Database</option>
                    <option value="service">Service</option>
                    <option value="component">Component</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="message">Message:</label>
                <textarea id="message" rows="3" placeholder="Enter log message..." required></textarea>
            </div>
            
            <button type="submit">📤 Send Log</button>
            <button type="button" onclick="runTestLogs()">🧪 Run Test Logs</button>
        </form>
        
        <div id="log-result"></div>
    </div>

    <div class="container">
        <h2>📊 Output</h2>
        <div id="output"></div>
        <button onclick="clearOutput()">🗑️ Clear Output</button>
    </div>

    <script>
        // Load token info on page load
        document.addEventListener('DOMContentLoaded', function() {
            refreshTokenInfo();
        });

        // Function to refresh token information
        async function refreshTokenInfo() {
            try {
                const response = await fetch('/api/token-info');
                const data = await response.json();
                
                if (data.success && data.tokenInfo) {
                    const info = data.tokenInfo;
                    document.getElementById('token-status').innerHTML = `
                        <strong>Status:</strong> ${info.isExpired ? '❌ EXPIRED' : '✅ Valid'}<br>
                        <strong>Issued:</strong> ${new Date(info.issuedAt * 1000).toLocaleString()}<br>
                        <strong>Expires:</strong> ${new Date(info.expiresAt * 1000).toLocaleString()}<br>
                        <strong>Time until expiry:</strong> ${Math.floor(info.timeUntilExpiry / 60)} minutes
                    `;
                } else {
                    document.getElementById('token-status').innerHTML = '❌ Failed to load token info';
                }
            } catch (error) {
                document.getElementById('token-status').innerHTML = '❌ Error loading token info';
                console.error('Error:', error);
            }
        }

        // Handle form submission
        document.getElementById('logForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = {
                stack: document.getElementById('stack').value,
                level: document.getElementById('level').value,
                package: document.getElementById('package').value,
                message: document.getElementById('message').value
            };
            
            await sendLogEntry(formData);
        });

        // Function to send log entry
        async function sendLogEntry(logData) {
            try {
                addOutput(`📤 Sending log: ${logData.stack}/${logData.level}/${logData.package} - ${logData.message}`);
                
                const response = await fetch('/api/log', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(logData)
                });
                
                const result = await response.json();
                
                if (result.success) {
                    document.getElementById('log-result').innerHTML = '<div class="success">✅ Log sent successfully!</div>';
                    addOutput(`✅ Log sent successfully: ${JSON.stringify(result.logged, null, 2)}`);
                } else {
                    document.getElementById('log-result').innerHTML = `<div class="error">❌ Error: ${result.error}</div>`;
                    addOutput(`❌ Error: ${result.error}`);
                }
            } catch (error) {
                document.getElementById('log-result').innerHTML = `<div class="error">❌ Network error: ${error.message}</div>`;
                addOutput(`❌ Network error: ${error.message}`);
            }
        }

        // Function to run test logs
        async function runTestLogs() {
            const testLogs = [
                { stack: 'backend', level: 'error', package: 'handler', message: 'received string, expected bool' },
                { stack: 'frontend', level: 'info', package: 'auth', message: 'User logged in successfully' },
                { stack: 'backend', level: 'debug', package: 'db', message: 'Executing complex join query for user data' },
                { stack: 'backend', level: 'warn', package: 'middleware', message: 'Invalid email format detected for user ID 456' },
                { stack: 'frontend', level: 'debug', package: 'component', message: 'User interface element rendered.' },
                { stack: 'backend', level: 'info', package: 'service', message: 'Data processed by background service.' }
            ];
            
            addOutput('🧪 Running test logs...');
            
            for (const logData of testLogs) {
                await sendLogEntry(logData);
                await new Promise(resolve => setTimeout(resolve, 500)); // Small delay between requests
            }
            
            addOutput('🧪 Test logs completed!');
        }

        // Function to add output
        function addOutput(message) {
            const output = document.getElementById('output');
            const timestamp = new Date().toLocaleTimeString();
            output.textContent += `[${timestamp}] ${message}\n`;
            output.scrollTop = output.scrollHeight;
        }

        // Function to clear output
        function clearOutput() {
            document.getElementById('output').textContent = '';
        }
    </script>
</body>
</html>
