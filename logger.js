// logger.js

// NOTE: This is the updated auth token — store securely in production!
const authToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.vfwcJ9UlO786f5XWpuFFqxVB726ENchWGIzO95jOeqI";

// Function to log stuff to the service
async function sendLog(stackName, severity, pkgName, logMsg) {
    const apiUrl = "http://20.244.56.144/evaluation-service/logs";

    const dataToSend = {
        // Ensure values are lowercase as per API constraint
        stack: stackName.toLowerCase(),
        level: severity.toLowerCase(),
        package: pkgName.toLowerCase(),
        message: logMsg
    };

    if (!authToken) {
        console.error("No token! Can't send log.");
        return;
    }

    try {
        const res = await fetch(apiUrl, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
                "Authorization": `Bearer ${authToken}`
            },
            body: JSON.stringify(dataToSend)
        });

        if (res.ok) {
            // Changed message for clarity when successful
            console.log("Log sent successfully:", dataToSend);
        } else {
            const errMsg = await res.text();
            console.error(`Log failed: ${res.status} - ${errMsg}`);
            if (res.status === 401) {
                console.warn("Hmm, token expired? Might need to refresh it.");
            }
        }
    } catch (e) {
        console.error("Something went wrong sending log:", e);
    }
}

// --- CORRECTED TEST LOGS (USING ALLOWED VALUES FROM YOUR DOCUMENTATION) ---

// This example from the image already works as 'backend' and 'handler' are allowed.
console.log("Attempting to send Backend error log (should succeed):");
sendLog("backend", "error", "handler", "received string, expected bool");

// Frontend log example:
// 'stack' changed to 'web' (based on 'invalid web stack' error and common usage)
// 'package' changed to 'auth' (allowed for both frontend/backend applications)
console.log("Attempting to send Frontend info log:");
sendLog("web", "info", "auth", "User logged in successfully");

// Database log example:
// 'stack' changed to 'backend' (since 'db' package is listed under Backend Application)
// 'package' changed to 'db' (from allowed Backend Application packages)
console.log("Attempting to send DB debug log:");
sendLog("backend", "debug", "db", "Executing complex join query for user data");

// Backend warning log example:
// 'package' changed to 'middleware' (allowed for both, suitable for validation context)
console.log("Attempting to send Backend warning log:");
sendLog("backend", "warn", "middleware", "Invalid email format detected for user ID 456");

// Additional example for Frontend component (using 'web' stack and 'component' package)
console.log("Attempting to send Frontend component debug log:");
sendLog("web", "debug", "component", "User interface element rendered.");

// Additional example for Backend service (using 'backend' stack and 'service' package)
console.log("Attempting to send Backend service info log:");
sendLog("backend", "info", "service", "Data processed by background service.");